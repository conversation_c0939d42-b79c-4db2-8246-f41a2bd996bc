package com.university.management.service.impl;

import com.university.management.model.entity.Course;
import com.university.management.model.entity.Student;
import com.university.management.model.entity.Teacher;
import com.university.management.repository.CourseRepository;
import com.university.management.repository.StudentRepository;
import com.university.management.repository.TeacherRepository;
import com.university.management.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计服务实现类
 */
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private TeacherRepository teacherRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Override
    public Map<String, Object> getMonthlyStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取各实体的月度统计数据
        Map<String, Long> studentStats = getStudentMonthlyStats();
        Map<String, Long> teacherStats = getTeacherMonthlyStats();
        Map<String, Long> courseStats = getCourseMonthlyStats();
        
        // 转换为前端所需的格式
        List<Map<String, Object>> monthlyData = new ArrayList<>();
        
        // 获取所有月份（1-12）
        Set<String> allMonths = new TreeSet<>();
        allMonths.addAll(studentStats.keySet());
        allMonths.addAll(teacherStats.keySet());
        allMonths.addAll(courseStats.keySet());
        
        // 按月份顺序构建数据
        for (String month : allMonths) {
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", month);
            monthData.put("studentCount", studentStats.getOrDefault(month, 0L));
            monthData.put("teacherCount", teacherStats.getOrDefault(month, 0L));
            monthData.put("courseCount", courseStats.getOrDefault(month, 0L));
            monthlyData.add(monthData);
        }
        
        stats.put("monthlyStats", monthlyData);
        return stats;
    }

    @Override
    public Map<String, Long> getStudentMonthlyStats() {
        List<Student> students = studentRepository.findAll();
        
        // 按月份分组统计学生数量
        return students.stream()
                .filter(student -> student.getCreateTime() != null)
                .collect(Collectors.groupingBy(
                        student -> getMonthName(student.getCreateTime().getMonth()),
                        Collectors.counting()
                ));
    }

    @Override
    public Map<String, Long> getTeacherMonthlyStats() {
        List<Teacher> teachers = teacherRepository.findAll();
        
        // 按月份分组统计教师数量
        return teachers.stream()
                .filter(teacher -> teacher.getCreateTime() != null)
                .collect(Collectors.groupingBy(
                        teacher -> getMonthName(teacher.getCreateTime().getMonth()),
                        Collectors.counting()
                ));
    }

    @Override
    public Map<String, Long> getCourseMonthlyStats() {
        List<Course> courses = courseRepository.findAll();
        
        // 按月份分组统计课程数量
        return courses.stream()
                .filter(course -> course.getCreateTime() != null)
                .collect(Collectors.groupingBy(
                        course -> getMonthName(course.getCreateTime().getMonth()),
                        Collectors.counting()
                ));
    }
    
    /**
     * 获取月份名称（中文）
     * 
     * @param month 月份枚举
     * @return 月份名称
     */
    private String getMonthName(Month month) {
        return month.getDisplayName(TextStyle.FULL, Locale.CHINA);
    }
}