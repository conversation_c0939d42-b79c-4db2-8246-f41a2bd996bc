2025-07-27 04:54:14 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 40956 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 04:54:14 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 04:54:14 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 04:54:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 04:54:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 04:54:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168 ms. Found 11 JPA repository interfaces.
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 04:54:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 04:54:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 04:54:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 04:54:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 04:54:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2091 ms
2025-07-27 04:54:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 04:54:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 04:54:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 04:54:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 04:54:20 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0aca7c4b-ccfc-45ac-b2e5-e2ab9fc3f6a5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 04:54:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b25ce5e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b99e726, org.springframework.security.web.context.SecurityContextPersistenceFilter@56a8b19f, org.springframework.security.web.header.HeaderWriterFilter@e62d757, org.springframework.web.filter.CorsFilter@3a788fe0, org.springframework.security.web.authentication.logout.LogoutFilter@915c47b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@36c45b54, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@377a1763, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b841396, org.springframework.security.web.session.SessionManagementFilter@2be49c8c, org.springframework.security.web.access.ExceptionTranslationFilter@3003d288, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2474331d]
2025-07-27 04:54:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 04:54:22 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 8.412 seconds (JVM running for 8.801)
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 04:54:22 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 36 ms
2025-07-27 04:55:03 [http-nio-8083-exec-9] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 05:04:33 [http-nio-8083-exec-4] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 06:58:01 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 5512 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 06:58:01 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 06:58:01 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 06:58:02 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 06:58:02 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 06:58:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 190 ms. Found 11 JPA repository interfaces.
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 06:58:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 06:58:04 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 06:58:04 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 06:58:04 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 06:58:04 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 06:58:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2264 ms
2025-07-27 06:58:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 06:58:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 06:58:06 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 06:58:07 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 06:58:08 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 43944ff6-ab4d-4ff5-816e-4760f710a20f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 06:58:08 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@21a09c5a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18d76c14, org.springframework.security.web.context.SecurityContextPersistenceFilter@110b6c78, org.springframework.security.web.header.HeaderWriterFilter@7e641005, org.springframework.web.filter.CorsFilter@2c06b113, org.springframework.security.web.authentication.logout.LogoutFilter@2da273b3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@597f2d3f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@438a2337, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@517f9ba3, org.springframework.security.web.session.SessionManagementFilter@794cd751, org.springframework.security.web.access.ExceptionTranslationFilter@3c84c32f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1985f8e3]
2025-07-27 06:58:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 06:58:10 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 8.862 seconds (JVM running for 9.303)
2025-07-27 06:58:10 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 06:58:10 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 06:58:10 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 06:58:10 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 06:58:23 [http-nio-8083-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 06:58:23 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 06:58:23 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-27 06:58:41 [http-nio-8083-exec-6] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 07:00:43 [http-nio-8083-exec-4] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 07:22:47 [http-nio-8083-exec-7] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 07:28:41 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 25716 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 07:28:41 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 07:28:41 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 07:28:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 07:28:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 07:28:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 225 ms. Found 11 JPA repository interfaces.
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 07:28:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 07:28:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 07:28:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 07:28:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 07:28:46 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 07:28:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4033 ms
2025-07-27 07:28:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 07:28:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 07:28:49 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 07:28:49 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 07:28:51 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 5383fba0-232c-4f14-bac0-fb42d5ef9569

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 07:28:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@79be91eb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@626c569b, org.springframework.security.web.context.SecurityContextPersistenceFilter@5fb5ad40, org.springframework.security.web.header.HeaderWriterFilter@7ac5b4c, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@50cbcca7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@632cf7d3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@634e1b39, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@684ce74c, org.springframework.security.web.session.SessionManagementFilter@649b5891, org.springframework.security.web.access.ExceptionTranslationFilter@489110c2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@26c7b1c6]
2025-07-27 07:28:53 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 07:28:53 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 07:28:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 07:28:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 07:28:53 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 07:28:53 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 07:28:53 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-07-27 08:32:25 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 34504 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 08:32:25 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 08:32:25 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 08:32:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 08:32:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 08:32:27 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 242 ms. Found 11 JPA repository interfaces.
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:32:27 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 08:32:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 08:32:29 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 08:32:29 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 08:32:29 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 08:32:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3670 ms
2025-07-27 08:32:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 08:32:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 08:32:32 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:32:33 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 08:32:34 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c8543401-9a16-47f0-9da9-b640c48ca703

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 08:32:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@42d9e8d2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b93dd6a, org.springframework.security.web.context.SecurityContextPersistenceFilter@9690008, org.springframework.security.web.header.HeaderWriterFilter@65600fb3, org.springframework.web.filter.CorsFilter@12dae582, org.springframework.security.web.authentication.logout.LogoutFilter@76e4212, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@79cb8ffa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62ea8931, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@582a3b19, org.springframework.security.web.session.SessionManagementFilter@5f5297e3, org.springframework.security.web.access.ExceptionTranslationFilter@73e1ecd0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@35f7969d]
2025-07-27 08:32:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 08:32:37 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 12.621 seconds (JVM running for 13.406)
2025-07-27 08:32:37 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 08:32:37 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 08:32:37 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 08:32:37 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 08:34:21 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 08:34:21 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 08:34:21 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-07-27 08:34:48 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 3084 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 08:34:48 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 08:34:48 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 08:34:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 08:34:51 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 08:34:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 508 ms. Found 11 JPA repository interfaces.
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:34:52 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 08:34:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 08:34:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 08:34:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 08:34:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 08:34:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6105 ms
2025-07-27 08:34:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 08:34:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 08:34:59 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:35:00 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 08:35:04 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 022f1350-8091-4eeb-bdf1-8ff4bf98f811

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 08:35:04 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@19526f1d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d1c933, org.springframework.security.web.context.SecurityContextPersistenceFilter@7a3f08b6, org.springframework.security.web.header.HeaderWriterFilter@489110c2, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@204d9edf, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5fb5ad40, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@51b87df7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@79be91eb, org.springframework.security.web.session.SessionManagementFilter@1c1fa494, org.springframework.security.web.access.ExceptionTranslationFilter@3cc9632d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7d7ceca8]
2025-07-27 08:35:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 08:35:09 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 22.305 seconds (JVM running for 23.091)
2025-07-27 08:35:09 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 08:35:09 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 08:35:09 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 08:35:09 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 08:35:36 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 08:35:36 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 08:35:36 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-27 08:38:13 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:38:13 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 08:38:13 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 08:40:08 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 5900 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 08:40:08 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 08:40:08 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 08:40:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 08:40:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 08:40:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 193 ms. Found 11 JPA repository interfaces.
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:40:10 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 08:40:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 08:40:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 08:40:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 08:40:11 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 08:40:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2780 ms
2025-07-27 08:40:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 08:40:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 08:40:14 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:40:15 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 08:40:17 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 13c72212-3dd7-48ff-8059-00901a1e4056

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 08:40:17 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5beda06e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2474331d, org.springframework.security.web.context.SecurityContextPersistenceFilter@5128efc, org.springframework.security.web.header.HeaderWriterFilter@7d440378, org.springframework.web.filter.CorsFilter@7a64cb0c, org.springframework.security.web.authentication.logout.LogoutFilter@664c411d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@593d5f39, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2be49c8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@46977829, org.springframework.security.web.session.SessionManagementFilter@7746330a, org.springframework.security.web.access.ExceptionTranslationFilter@5e78dd7f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7c98bac0]
2025-07-27 08:40:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 08:40:19 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 11.405 seconds (JVM running for 12.01)
2025-07-27 08:40:19 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 08:40:19 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 08:40:20 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 08:40:20 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 08:43:58 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 26912 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 08:43:58 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 08:43:58 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 08:44:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 08:44:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 08:44:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 293 ms. Found 11 JPA repository interfaces.
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 08:44:00 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 08:44:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 08:44:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 08:44:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 08:44:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 08:44:02 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3470 ms
2025-07-27 08:44:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 08:44:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 08:44:06 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:44:07 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 08:44:08 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: eb7e3971-abbe-41c3-ae28-2a4aa1221bca

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 08:44:08 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@44992466, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55bf08a5, org.springframework.security.web.context.SecurityContextPersistenceFilter@61f62b80, org.springframework.security.web.header.HeaderWriterFilter@5e537465, org.springframework.web.filter.CorsFilter@29bbc63c, org.springframework.security.web.authentication.logout.LogoutFilter@7b57587e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@22cf6c9c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2140de63, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@623cd12e, org.springframework.security.web.session.SessionManagementFilter@5da08e9a, org.springframework.security.web.access.ExceptionTranslationFilter@438a2337, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@596343e7]
2025-07-27 08:44:09 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 08:44:09 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 08:44:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 08:44:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 08:44:09 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 08:44:09 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 08:44:09 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-07-27 08:44:14 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 08:44:14 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 08:44:14 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-27 08:45:15 [http-nio-8083-exec-9] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 09:10:38 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 13204 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:10:38 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:10:38 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:10:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:10:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:10:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 239 ms. Found 11 JPA repository interfaces.
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:10:40 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:10:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:10:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:10:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:10:41 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:10:41 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2598 ms
2025-07-27 09:10:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:10:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:10:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:10:43 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:10:44 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0a0218b7-25f7-49e1-b372-2b0b3a3623ad

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:10:45 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@17da6e45, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6af10aa7, org.springframework.security.web.context.SecurityContextPersistenceFilter@5d267575, org.springframework.security.web.header.HeaderWriterFilter@31620971, org.springframework.web.filter.CorsFilter@7a64cb0c, org.springframework.security.web.authentication.logout.LogoutFilter@14ce13fa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2be49c8c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@36c45b54, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15eaa2ce, org.springframework.security.web.session.SessionManagementFilter@593d5f39, org.springframework.security.web.access.ExceptionTranslationFilter@615f61ec, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@517f9ba3]
2025-07-27 09:10:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 09:10:46 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 8.792 seconds (JVM running for 9.41)
2025-07-27 09:10:46 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:10:46 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:10:46 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 09:10:46 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 09:12:36 [http-nio-8083-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 09:12:36 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 09:12:36 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-27 09:34:07 [http-nio-8083-exec-9] INFO  c.u.m.controller.AuthController - 用户登录: admin
2025-07-27 09:35:01 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 26392 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:35:01 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:35:01 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:35:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:35:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:35:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 224 ms. Found 11 JPA repository interfaces.
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:35:03 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:35:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:35:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:35:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:35:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:35:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4114 ms
2025-07-27 09:35:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:35:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:35:08 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:35:09 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:35:11 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d3afee04-9f75-454b-aca9-2d40cf99dea6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:35:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7d7ceca8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2bba35ef, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c1fa494, org.springframework.security.web.header.HeaderWriterFilter@6a8a551e, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@53a301f3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@649b5891, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5fb5ad40, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@26c7b1c6, org.springframework.security.web.session.SessionManagementFilter@150fc7a7, org.springframework.security.web.access.ExceptionTranslationFilter@4245bf68, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5ba1b62e]
2025-07-27 09:35:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 09:35:15 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 14.808 seconds (JVM running for 15.857)
2025-07-27 09:35:15 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:35:15 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:35:15 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 09:35:15 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 09:35:22 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 09:35:22 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 09:35:22 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-27 09:36:58 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 36620 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:36:58 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:36:58 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:37:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:37:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:37:01 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 289 ms. Found 11 JPA repository interfaces.
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:02 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:37:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:37:03 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:37:03 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:37:03 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:37:03 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4796 ms
2025-07-27 09:37:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:37:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:37:08 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:37:09 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:37:11 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 4f573786-f32f-442f-8779-094e180b529d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:37:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@38811103, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@787d1f9c, org.springframework.security.web.context.SecurityContextPersistenceFilter@76e4212, org.springframework.security.web.header.HeaderWriterFilter@2f8c4fae, org.springframework.web.filter.CorsFilter@619bfe29, org.springframework.security.web.authentication.logout.LogoutFilter@798cf6d2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72af90e8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4c635edc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2becfd4c, org.springframework.security.web.session.SessionManagementFilter@3be3e76c, org.springframework.security.web.access.ExceptionTranslationFilter@62ea8931, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6f15f52a]
2025-07-27 09:37:13 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 09:37:13 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:37:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:37:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:37:13 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 09:37:13 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 09:37:13 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-07-27 09:37:23 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 27760 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:37:23 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:37:23 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:37:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:37:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:37:26 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 718 ms. Found 11 JPA repository interfaces.
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:37:26 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:37:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:37:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:37:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:37:29 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:37:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5671 ms
2025-07-27 09:37:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:37:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:37:33 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:37:35 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:37:37 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6110ed7b-6ce5-45c7-bcf8-4ac66ca5cd3b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:37:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2becfd4c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c71c889, org.springframework.security.web.context.SecurityContextPersistenceFilter@72af90e8, org.springframework.security.web.header.HeaderWriterFilter@71166348, org.springframework.web.filter.CorsFilter@12dae582, org.springframework.security.web.authentication.logout.LogoutFilter@77c66a4f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7faa0680, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@45bbc52f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4cc8d70d, org.springframework.security.web.session.SessionManagementFilter@10ed037a, org.springframework.security.web.access.ExceptionTranslationFilter@2f8c4fae, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@341c6ac2]
2025-07-27 09:37:40 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 09:37:40 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:37:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:37:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:37:40 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 09:37:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 09:37:40 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-07-27 09:38:14 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 34564 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:38:14 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:38:14 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 496 ms. Found 11 JPA repository interfaces.
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:38:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:38:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:38:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:38:21 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:38:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6603 ms
2025-07-27 09:38:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:38:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:38:26 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:38:27 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:38:30 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 994d6a5a-4c19-48c0-856e-f2ae5880a661

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:38:30 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2bba35ef, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@26c7b1c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@77c66a4f, org.springframework.security.web.header.HeaderWriterFilter@3cc9632d, org.springframework.web.filter.CorsFilter@14f5da2c, org.springframework.security.web.authentication.logout.LogoutFilter@7cf8f45a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7a3f08b6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@615439f7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19526f1d, org.springframework.security.web.session.SessionManagementFilter@55d8c2c4, org.springframework.security.web.access.ExceptionTranslationFilter@45bbc52f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@65bd19bf]
2025-07-27 09:38:32 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 09:38:32 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:38:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:38:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:38:33 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 09:38:33 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 09:38:33 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-07-27 09:40:03 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 33600 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:40:03 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:40:03 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:40:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:40:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:40:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 281 ms. Found 11 JPA repository interfaces.
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:40:06 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:40:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 09:40:08 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:40:08 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:40:08 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:40:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4858 ms
2025-07-27 09:40:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:40:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:40:11 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:40:12 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:40:14 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d683e267-d62b-4510-9355-a5ac26b1faab

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:40:15 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4d69d288, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@437281c5, org.springframework.security.web.context.SecurityContextPersistenceFilter@4d81e83a, org.springframework.security.web.header.HeaderWriterFilter@615439f7, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@2b0454d2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7cf8f45a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b2aafbc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@787178b1, org.springframework.security.web.session.SessionManagementFilter@751d7425, org.springframework.security.web.access.ExceptionTranslationFilter@1be52861, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4b28a7bf]
2025-07-27 09:40:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 09:40:18 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 15.456 seconds (JVM running for 16.259)
2025-07-27 09:40:18 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:40:18 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:40:18 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 09:40:18 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 09:40:45 [http-nio-8084-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 09:40:45 [http-nio-8084-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 09:40:45 [http-nio-8084-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-27 09:42:41 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:43:41 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 8796 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:43:41 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:43:41 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:43:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:43:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:43:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 247 ms. Found 11 JPA repository interfaces.
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:43:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:43:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 09:43:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:43:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:43:46 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:43:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4344 ms
2025-07-27 09:43:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:43:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:43:49 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:43:50 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:43:52 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b8cafd23-1ba5-4f01-9d65-a0dd8675b467

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:43:52 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2becfd4c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c71c889, org.springframework.security.web.context.SecurityContextPersistenceFilter@72af90e8, org.springframework.security.web.header.HeaderWriterFilter@71166348, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@77c66a4f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7faa0680, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@45bbc52f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4cc8d70d, org.springframework.security.web.session.SessionManagementFilter@10ed037a, org.springframework.security.web.access.ExceptionTranslationFilter@2f8c4fae, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@341c6ac2]
2025-07-27 09:43:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 09:43:56 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 14.978 seconds (JVM running for 15.815)
2025-07-27 09:43:56 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:43:56 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:43:56 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 09:43:56 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 09:45:03 [http-nio-8084-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 09:45:03 [http-nio-8084-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 09:45:03 [http-nio-8084-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-27 09:46:48 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 28484 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:46:48 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:46:48 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:46:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:46:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:46:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 157 ms. Found 11 JPA repository interfaces.
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:46:50 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:46:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 09:46:50 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:46:50 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:46:50 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:46:50 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2116 ms
2025-07-27 09:46:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:46:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:46:52 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:46:53 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:46:55 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 621f260b-6582-492a-b459-0274819aea9d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:46:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7011eee0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5576eef4, org.springframework.security.web.context.SecurityContextPersistenceFilter@7746330a, org.springframework.security.web.header.HeaderWriterFilter@42014a, org.springframework.web.filter.CorsFilter@7a64cb0c, org.springframework.security.web.authentication.logout.LogoutFilter@424461ad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@252459b2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f27643d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@76629f82, org.springframework.security.web.session.SessionManagementFilter@110b6c78, org.springframework.security.web.access.ExceptionTranslationFilter@2ccfd2cb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@269a37bc]
2025-07-27 09:46:56 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8084 is already in use
2025-07-27 09:46:56 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:46:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:46:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:46:56 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 09:46:56 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 09:46:56 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8084 was already in use.

Action:

Identify and stop the process that's listening on port 8084 or configure this application to listen on another port.

2025-07-27 09:49:12 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 32560 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:49:12 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:49:12 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:49:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:49:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:49:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142 ms. Found 11 JPA repository interfaces.
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:49:13 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:49:14 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 09:49:14 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:49:14 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:49:14 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:49:14 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1962 ms
2025-07-27 09:49:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:49:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:49:16 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:49:16 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:49:17 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: cf363036-a0bb-4815-9606-ce5f0cd5b81c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:49:17 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@46977829, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@24eb71f6, org.springframework.security.web.context.SecurityContextPersistenceFilter@593d5f39, org.springframework.security.web.header.HeaderWriterFilter@7597ae32, org.springframework.web.filter.CorsFilter@7a64cb0c, org.springframework.security.web.authentication.logout.LogoutFilter@569d7074, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4f27643d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@31f5580b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@12b34bd5, org.springframework.security.web.session.SessionManagementFilter@252459b2, org.springframework.security.web.access.ExceptionTranslationFilter@7d440378, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6fc9c0cc]
2025-07-27 09:49:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 09:49:19 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 7.79 seconds (JVM running for 8.228)
2025-07-27 09:49:19 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:49:19 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:49:19 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 09:49:19 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 09:50:23 [http-nio-8084-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 09:50:23 [http-nio-8084-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 09:50:23 [http-nio-8084-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-27 09:51:47 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 31856 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:51:47 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:51:47 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:51:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:51:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:51:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 226 ms. Found 11 JPA repository interfaces.
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:51:49 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:51:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 09:51:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:51:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:51:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:51:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3664 ms
2025-07-27 09:51:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:51:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 09:51:54 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:51:55 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:51:56 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c3f3e267-16aa-4dc9-9f6a-0abf5b5646d1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:51:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5d2a86c0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ba1b62e, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ac5b4c, org.springframework.security.web.header.HeaderWriterFilter@7faa0680, org.springframework.web.filter.CorsFilter@5b057c8c, org.springframework.security.web.authentication.logout.LogoutFilter@751d7425, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55d8c2c4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77c66a4f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@65bd19bf, org.springframework.security.web.session.SessionManagementFilter@53982523, org.springframework.security.web.access.ExceptionTranslationFilter@72af90e8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@70fede7d]
2025-07-27 09:51:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 09:51:59 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 13.133 seconds (JVM running for 14.115)
2025-07-27 09:51:59 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 09:52:00 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 09:52:00 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 09:52:00 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 10:00:37 [http-nio-8084-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 10:00:37 [http-nio-8084-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 10:00:37 [http-nio-8084-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-27 10:01:14 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 1856 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:01:14 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:01:14 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:01:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:01:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:01:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 260 ms. Found 11 JPA repository interfaces.
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:01:17 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 10:01:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 10:01:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:01:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:01:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:01:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3977 ms
2025-07-27 10:01:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:01:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:01:21 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:01:22 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:01:24 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 50f6fa70-1a95-477a-89fa-4f010161c9ed

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:01:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@d36c1c3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@96abc76, org.springframework.security.web.context.SecurityContextPersistenceFilter@7eaa2bc6, org.springframework.security.web.header.HeaderWriterFilter@1be52861, org.springframework.web.filter.CorsFilter@619bfe29, org.springframework.security.web.authentication.logout.LogoutFilter@4b93dd6a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4d81e83a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62cf6a84, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4d69d288, org.springframework.security.web.session.SessionManagementFilter@634e1b39, org.springframework.security.web.access.ExceptionTranslationFilter@649b5891, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@68bd8ca7]
2025-07-27 10:01:26 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8084 is already in use
2025-07-27 10:01:26 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:01:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 10:01:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 10:01:26 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 10:01:27 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 10:01:27 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8084 was already in use.

Action:

Identify and stop the process that's listening on port 8084 or configure this application to listen on another port.

2025-07-27 10:02:44 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 36048 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:02:44 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:02:44 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:02:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:02:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:02:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 251 ms. Found 11 JPA repository interfaces.
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:02:47 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 10:02:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 10:02:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:02:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:02:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:02:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4218 ms
2025-07-27 10:02:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:02:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:02:51 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:02:53 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:02:55 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 52f56af5-aebc-4e32-bd2a-bfc07a04b8dd

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:02:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2c47a053, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d2a86c0, org.springframework.security.web.context.SecurityContextPersistenceFilter@700b9e6b, org.springframework.security.web.header.HeaderWriterFilter@aa1bb14, org.springframework.web.filter.CorsFilter@619bfe29, org.springframework.security.web.authentication.logout.LogoutFilter@67d8faec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@150fc7a7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1c1fa494, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ba1b62e, org.springframework.security.web.session.SessionManagementFilter@3cc9632d, org.springframework.security.web.access.ExceptionTranslationFilter@23121d14, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@79e10fb4]
2025-07-27 10:02:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 10:02:59 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 17.023 seconds (JVM running for 17.987)
2025-07-27 10:02:59 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 10:02:59 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 10:03:00 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 10:03:00 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 10:04:39 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 35736 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:04:39 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:04:39 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:04:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:04:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:04:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 470 ms. Found 11 JPA repository interfaces.
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 10:04:44 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 10:04:46 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8084 (http)
2025-07-27 10:04:46 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:04:46 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:04:46 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:04:46 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6859 ms
2025-07-27 10:04:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:04:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:04:52 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:04:54 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:04:57 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0ff8348b-34c9-4965-b031-4a4c12d28703

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:04:58 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@79be91eb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@626c569b, org.springframework.security.web.context.SecurityContextPersistenceFilter@5fb5ad40, org.springframework.security.web.header.HeaderWriterFilter@7ac5b4c, org.springframework.web.filter.CorsFilter@652a7737, org.springframework.security.web.authentication.logout.LogoutFilter@50cbcca7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@632cf7d3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@634e1b39, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@684ce74c, org.springframework.security.web.session.SessionManagementFilter@649b5891, org.springframework.security.web.access.ExceptionTranslationFilter@489110c2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@26c7b1c6]
2025-07-27 10:05:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8084 (http) with context path ''
2025-07-27 10:05:06 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 27.613 seconds (JVM running for 28.742)
2025-07-27 10:05:06 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 10:05:06 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 10:05:06 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 16 common frames omitted
2025-07-27 10:05:06 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 10:09:42 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 13656 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:09:42 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:09:42 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:09:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:09:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:09:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 156 ms. Found 11 JPA repository interfaces.
2025-07-27 10:09:43 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management]' package. Please check your configuration.
2025-07-27 10:09:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 10:09:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:09:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:09:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:09:44 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1848 ms
2025-07-27 10:09:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:09:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:09:45 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:09:46 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:09:47 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c17be26d-fffb-49f3-aa79-46229a36bff8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:09:47 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@8829ecd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1985f8e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@cf315f1, org.springframework.security.web.header.HeaderWriterFilter@22ab3e3b, org.springframework.web.filter.CorsFilter@34edd065, org.springframework.security.web.authentication.logout.LogoutFilter@49aa6081, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6d1c15cb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@794cd751, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ba70b3b, org.springframework.security.web.session.SessionManagementFilter@36c8e1c6, org.springframework.security.web.access.ExceptionTranslationFilter@652855c4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a479c0c]
2025-07-27 10:09:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 10:09:48 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 6.807 seconds (JVM running for 7.232)
2025-07-27 10:09:48 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 10:09:48 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 10:09:48 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:19)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 10:09:48 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 10:10:10 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 10:10:10 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 10:10:10 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-27 10:23:03 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 5764 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:23:03 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:23:03 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:23:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:23:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:23:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 176 ms. Found 11 JPA repository interfaces.
2025-07-27 10:23:04 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management]' package. Please check your configuration.
2025-07-27 10:23:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 10:23:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:23:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:23:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:23:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1986 ms
2025-07-27 10:23:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:23:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:23:07 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:23:08 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:23:09 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 7ed3f0a9-7804-4201-9aba-97531e4b8b97

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:23:09 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@498102a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8829ecd, org.springframework.security.web.context.SecurityContextPersistenceFilter@5e78dd7f, org.springframework.security.web.header.HeaderWriterFilter@619dc070, org.springframework.web.filter.CorsFilter@34edd065, org.springframework.security.web.authentication.logout.LogoutFilter@5da08e9a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7d440378, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@31620971, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1985f8e3, org.springframework.security.web.session.SessionManagementFilter@2ccfd2cb, org.springframework.security.web.access.ExceptionTranslationFilter@56f8ab05, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3ca43e5d]
2025-07-27 10:23:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 10:23:10 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 7.546 seconds (JVM running for 7.936)
2025-07-27 10:23:10 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 10:23:10 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 10:23:10 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:19)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 10:23:10 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 10:23:14 [http-nio-8083-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 10:23:14 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 10:23:14 [http-nio-8083-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-07-27 10:24:33 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 19560 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 10:24:33 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 10:24:33 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 10:24:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 10:24:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 10:24:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 215 ms. Found 11 JPA repository interfaces.
2025-07-27 10:24:35 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management]' package. Please check your configuration.
2025-07-27 10:24:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 10:24:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 10:24:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 10:24:38 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 10:24:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4884 ms
2025-07-27 10:24:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 10:24:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 10:24:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:24:43 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 10:24:44 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: fed206c9-8e23-4f71-9a33-b21dd9d5e3e5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 10:24:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@21a09c5a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18d76c14, org.springframework.security.web.context.SecurityContextPersistenceFilter@110b6c78, org.springframework.security.web.header.HeaderWriterFilter@7e641005, org.springframework.web.filter.CorsFilter@34edd065, org.springframework.security.web.authentication.logout.LogoutFilter@2da273b3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@597f2d3f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@438a2337, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@517f9ba3, org.springframework.security.web.session.SessionManagementFilter@794cd751, org.springframework.security.web.access.ExceptionTranslationFilter@3c84c32f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1985f8e3]
2025-07-27 10:24:45 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 10:24:45 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 10:24:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 10:24:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 10:24:45 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 10:24:45 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 10:24:45 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

