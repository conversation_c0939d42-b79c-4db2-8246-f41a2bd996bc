package com.university.management.controller;

import com.university.management.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 统计控制器
 * 提供统计相关的API端点
 */
@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 获取月度统计数据
     * 
     * @return 月度统计数据
     */
    @GetMapping("/monthly")
    public ResponseEntity<Map<String, Object>> getMonthlyStats() {
        Map<String, Object> stats = statisticsService.getMonthlyStats();
        return ResponseEntity.ok(stats);
    }
}