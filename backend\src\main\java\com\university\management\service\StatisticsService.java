package com.university.management.service;

import java.util.Map;

/**
 * 统计服务接口
 * 提供各种统计数据的方法
 */
public interface StatisticsService {
    
    /**
     * 获取学生、教师和课程的月度统计数据
     * 
     * @return 月度统计数据，包含学生、教师和课程的月度数量
     */
    Map<String, Object> getMonthlyStats();
    
    /**
     * 获取学生的月度统计数据
     * 
     * @return 学生月度统计数据
     */
    Map<String, Long> getStudentMonthlyStats();
    
    /**
     * 获取教师的月度统计数据
     * 
     * @return 教师月度统计数据
     */
    Map<String, Long> getTeacherMonthlyStats();
    
    /**
     * 获取课程的月度统计数据
     * 
     * @return 课程月度统计数据
     */
    Map<String, Long> getCourseMonthlyStats();
}