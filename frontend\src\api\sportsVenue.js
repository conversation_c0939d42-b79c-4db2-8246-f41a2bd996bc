import request from '@/utils/request'

// 获取体育场馆列表
export function getSportsVenues() {
  return request({
    url: '/api/sports-venues',
    method: 'get'
  })
}

// 分页获取体育场馆列表
export function getSportsVenuesByPage(query) {
  return request({
    url: '/api/sports-venues/page',
    method: 'get',
    params: query
  })
}

// 根据ID获取体育场馆
export function getSportsVenueById(id) {
  return request({
    url: `/api/sports-venues/${id}`,
    method: 'get'
  })
}

// 创建体育场馆
export function createSportsVenue(data) {
  return request({
    url: '/api/sports-venues',
    method: 'post',
    data
  })
}

// 更新体育场馆
export function updateSportsVenue(id, data) {
  return request({
    url: `/api/sports-venues/${id}`,
    method: 'put',
    data
  })
}

// 删除体育场馆
export function deleteSportsVenue(id) {
  return request({
    url: `/api/sports-venues/${id}`,
    method: 'delete'
  })
}

// 获取场馆类型统计
export function getSportsVenueTypeStats() {
  return request({
    url: '/api/sports-venues/stats/type',
    method: 'get'
  })
}

// 获取可用场馆数量
export function getAvailableSportsVenueCount() {
  return request({
    url: '/api/sports-venues/stats/available',
    method: 'get'
  })
}

// 条件查询体育场馆
export function searchSportsVenues(query) {
  return request({
    url: '/api/sports-venues/search',
    method: 'get',
    params: query
  })
}