import request from '@/utils/request'

/**
 * 获取所有教室
 * @returns {Promise<Object>} 教室列表
 */
export function getAllClassrooms() {
  return request({
    url: '/api/classrooms',
    method: 'get'
  })
}

/**
 * 分页获取教室
 * @param {Object} params 分页参数
 * @returns {Promise<Object>} 分页教室列表
 */
export function getClassroomsByPage(params) {
  return request({
    url: '/api/classrooms/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取教室
 * @param {Number} id 教室ID
 * @returns {Promise<Object>} 教室信息
 */
export function getClassroomById(id) {
  return request({
    url: `/api/classrooms/${id}`,
    method: 'get'
  })
}

/**
 * 创建教室
 * @param {Object} data 教室信息
 * @returns {Promise<Object>} 创建结果
 */
export function createClassroom(data) {
  return request({
    url: '/api/classrooms',
    method: 'post',
    data
  })
}

/**
 * 更新教室
 * @param {Number} id 教室ID
 * @param {Object} data 教室信息
 * @returns {Promise<Object>} 更新结果
 */
export function updateClassroom(id, data) {
  return request({
    url: `/api/classrooms/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除教室
 * @param {Number} id 教室ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteClassroom(id) {
  return request({
    url: `/api/classrooms/${id}`,
    method: 'delete'
  })
}