package com.university.management.controller;

import com.university.management.model.common.RestResult;
import com.university.management.model.common.ResultCodeConstant;
import com.university.management.model.dto.LoginDTO;
import com.university.management.model.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList; // 导入 ArrayList

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@Api(tags = "认证管理")
public class AuthController {

    private static final Logger log = LoggerFactory.getLogger(AuthController.class);

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public RestResult<Map<String, Object>> login(@RequestBody @Valid LoginDTO loginDTO) {
        log.info("用户登录: {}", loginDTO.getUsername());
        
        // 这里简化处理，只要用户名是admin，密码是123456就认为登录成功
        if ("admin".equals(loginDTO.getUsername()) && "123456".equals(loginDTO.getPassword())) {
            Map<String, Object> result = new HashMap<>();
            // 生成一个模拟的token
            String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6ImFkbWluIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
            result.put("token", token);
            return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, result);
        } else {
            return new RestResult<>(ResultCodeConstant.CODE_000001, "用户名或密码错误");
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info")
    @ApiOperation("获取当前用户信息")
    public RestResult<UserInfoVO> getUserInfo() {
        // 这里简化处理，返回一个模拟的用户信息
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.setUserId(1);
        userInfo.setUsername("admin");
        userInfo.setRealName("系统管理员");
        userInfo.setAvatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif");
        // 调用 createMockRoles 获取模拟角色数据
        List<Map<String, Object>> mockRoles = createMockRoles();
        // 将角色名称提取出来，作为 String[] 返回
        String[] rolesArray = mockRoles.stream()
                                       .map(role -> (String) role.get("roleName"))
                                       .toArray(String[]::new);
        userInfo.setRoles(rolesArray);
        userInfo.setPermissions(new String[]{"*:*:*"});
        
        return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, userInfo);
    }

    /**
     * 用户登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public RestResult<Void> logout() {
        return new RestResult<>(ResultCodeConstant.CODE_000000, "登出成功");
    }

    /**
     * 创建模拟角色数据 (从 SimpleRoleController 复制过来)
     */
    private List<Map<String, Object>> createMockRoles() {
        List<Map<String, Object>> roles = new ArrayList<>();
        
        Map<String, Object> role1 = new HashMap<>();
        role1.put("id", 1);
        role1.put("roleName", "超级管理员");
        role1.put("roleCode", "SUPER_ADMIN");
        role1.put("description", "系统超级管理员，拥有所有权限");
        role1.put("status", 0);
        role1.put("createTime", "2024-01-01 10:00:00");
        roles.add(role1);
        
        Map<String, Object> role2 = new HashMap<>();
        role2.put("id", 2);
        role2.put("roleName", "系统管理员");
        role2.put("roleCode", "ADMIN");
        role2.put("description", "系统管理员，负责系统配置和用户管理");
        role2.put("status", 0);
        role2.put("createTime", "2024-01-01 10:00:00");
        roles.add(role2);
        
        Map<String, Object> role3 = new HashMap<>();
        role3.put("id", 3);
        role3.put("roleName", "教务管理员");
        role3.put("roleCode", "ACADEMIC_ADMIN");
        role3.put("description", "教务管理员，负责课程和成绩管理");
        role3.put("status", 0);
        role3.put("createTime", "2024-01-01 10:00:00");
        roles.add(role3);
        
        Map<String, Object> role4 = new HashMap<>();
        role4.put("id", 4);
        role4.put("roleName", "教师");
        role4.put("roleCode", "TEACHER");
        role4.put("description", "教师角色，可以管理自己的课程和学生");
        role4.put("status", 0);
        role4.put("createTime", "2024-01-01 10:00:00");
        roles.add(role4);
        
        Map<String, Object> role5 = new HashMap<>();
        role5.put("id", 5);
        role5.put("roleName", "学生");
        role5.put("roleCode", "STUDENT");
        role5.put("description", "学生角色，可以查看课程和成绩");
        role5.put("status", 0);
        role5.put("createTime", "2024-01-01 10:00:00");
        roles.add(role5);
        
        Map<String, Object> role6 = new HashMap<>();
        role6.put("id", 6);
        role6.put("roleName", "访客");
        role6.put("roleCode", "GUEST");
        role6.put("description", "访客角色，只能查看基本信息");
        role6.put("status", 1);
        role6.put("createTime", "2024-01-01 10:00:00");
        roles.add(role6);
        
        return roles;
    }
}