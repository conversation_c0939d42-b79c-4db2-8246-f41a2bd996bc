<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>大学学生管理系统</title>
    
    <!-- 第三方脚本错误过滤器 -->
    <script>
      // 早期错误捕获，在Vue应用启动前
      (function() {
        const originalConsoleError = console.error
        const originalConsoleWarn = console.warn
        
        // 过滤控制台错误显示
        console.error = function(...args) {
          const message = args.join(' ')
          if (message.includes('qk-content') || 
              message.includes('DraggableContainer') || 
              message.includes('selector is invalid') ||
              message.includes('chrome-extension://')) {
            console.info('🔍 已过滤第三方脚本错误:', message)
            return
          }
          originalConsoleError.apply(console, args)
        }
        
        console.warn = function(...args) {
          const message = args.join(' ')
          if (message.includes('qk-content') || 
              message.includes('DraggableContainer') || 
              message.includes('selector is invalid')) {
            return // 静默忽略
          }
          originalConsoleWarn.apply(console, args)
        }
        
        // 最早期的错误捕获
        window.addEventListener('error', function(event) {
          if (event.filename && event.filename.includes('qk-content.js')) {
            event.preventDefault()
            event.stopImmediatePropagation()
            return false
          }
        }, true) // 使用捕获阶段，优先级最高
        
        console.info('✅ 第三方脚本错误过滤器已启用')
      })()
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
