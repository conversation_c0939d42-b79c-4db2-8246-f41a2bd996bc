import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './style.css'

// 导入数据模式切换工具
import { showCurrentDataMode, autoSwitchDataMode } from './utils/switchToRealData'

// 全局错误处理 - 过滤第三方脚本错误
window.addEventListener('error', (event) => {
  // 过滤掉第三方脚本的错误
  if (event.filename && (
    event.filename.includes('qk-content.js') ||
    event.filename.includes('chrome-extension://') ||
    event.filename.includes('moz-extension://') ||
    event.filename.includes('safari-extension://') ||
    event.message.includes('DraggableContainer') ||
    event.message.includes('selector is invalid')
  )) {
    console.warn('🔍 过滤第三方脚本错误:', event.message)
    event.preventDefault()
    event.stopPropagation()
    return false
  }
})

// 全局Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && typeof event.reason === 'string' && (
    event.reason.includes('qk-content') ||
    event.reason.includes('DraggableContainer') ||
    event.reason.includes('selector is invalid')
  )) {
    console.warn('🔍 过滤第三方脚本Promise错误:', event.reason)
    event.preventDefault()
    return false
  }
})

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 挂载状态管理
app.use(createPinia())

// 挂载路由
app.use(router)

// 挂载Element Plus
app.use(ElementPlus, { size: 'default' })

// 全局异常处理
app.config.errorHandler = (err, vm, info) => {
  // 过滤第三方脚本错误
  if (err && err.stack && (
    err.stack.includes('qk-content.js') ||
    err.stack.includes('chrome-extension://') ||
    err.stack.includes('DraggableContainer')
  )) {
    console.warn('🔍 Vue过滤第三方脚本错误:', err.message)
    return
  }
  
  // 记录真正的应用错误
  console.error('❌ Vue应用错误:', err, info)
}

// 挂载应用
app.mount('#app')

// 应用启动后检查数据模式
setTimeout(() => {
  console.log('🚀 大学学生管理系统启动完成')
  showCurrentDataMode()

  // 在开发环境下自动检测数据模式
  if (import.meta.env.DEV) {
    console.log('🔍 开发环境：自动检测数据模式...')
    // autoSwitchDataMode() // 取消注释以启用自动检测
  }
}, 1000)
