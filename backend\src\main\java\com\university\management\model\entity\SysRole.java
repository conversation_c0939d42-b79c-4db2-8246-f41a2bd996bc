package com.university.management.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统角色实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SysRole", description = "系统角色信息")
@Entity
public class SysRole extends BaseEntity {

    @ApiModelProperty(value = "角色名称")
    @Column(unique = true, nullable = false, length = 50)
    private String roleName;

    @ApiModelProperty(value = "角色编码")
    @Column(unique = true, nullable = false, length = 50)
    private String roleCode;

    @ApiModelProperty(value = "角色描述")
    @Column(length = 200)
    private String description;

    @ApiModelProperty(value = "状态(0-正常，1-禁用)")
    private Integer status;

    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private List<SysUser> users = new ArrayList<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_role_menu",
            joinColumns = @JoinColumn(name = "role_id"),
            inverseJoinColumns = @JoinColumn(name = "menu_id")
    )
    private List<SysMenu> menus = new ArrayList<>();
}