<template>
  <div class="dashboard-container">
    <h2>仪表盘</h2>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="card-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-primary">
            <div class="card-icon">
              <el-icon :size="30"><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">学生总数</div>
              <div class="card-value">{{ statistics.studentCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.studentGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.studentGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-success">
            <div class="card-icon">
              <el-icon :size="30"><Avatar /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">教师总数</div>
              <div class="card-value">{{ statistics.teacherCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.teacherGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.teacherGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-warning">
            <div class="card-icon">
              <el-icon :size="30"><Reading /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">课程总数</div>
              <div class="card-value">{{ statistics.courseCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.courseGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.courseGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-danger">
            <div class="card-icon">
              <el-icon :size="30"><School /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">教室总数</div>
              <div class="card-value">{{ statistics.classroomCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.classroomGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.classroomGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第一行图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>学生专业分布</span>
              <el-radio-group v-model="chartFilters.studentMajorType" size="small">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="top5">Top 5</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="studentMajorChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>学生年级分布</span>
              <el-radio-group v-model="chartFilters.studentGradeView" size="small">
                <el-radio-button label="bar">柱状图</el-radio-button>
                <el-radio-button label="pie">饼图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="studentGradeChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>教师部门分布</span>
            </div>
          </template>
          <div id="teacherDepartmentChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>课程学分分布</span>
            </div>
          </template>
          <div id="courseCreditChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第三行图表（趋势图） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>年度趋势分析</span>
              <el-select v-model="chartFilters.trendType" size="small" style="width: 150px">
                <el-option label="学生数量" value="student"></el-option>
                <el-option label="教师数量" value="teacher"></el-option>
                <el-option label="课程数量" value="course"></el-option>
              </el-select>
            </div>
          </template>
          <div id="trendChart" class="chart-container" style="height: 380px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第四行（通知和快捷链接） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>系统通知</span>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity?.timestamp || ''"
              :type="activity?.type || 'info'"
              :color="activity?.color || '#909399'"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>快捷菜单</span>
            </div>
          </template>
          <div class="quick-links">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(link, index) in quickLinks" :key="index">
                <div class="quick-link-item" @click="navigateTo(link.path)">
                  <el-icon :size="36">
                    <component :is="link.icon" />
                  </el-icon>
                  <span>{{ link.title }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { User, Avatar, Reading, School, TrendCharts, Management, House, Collection } from '@element-plus/icons-vue'

// API 数据导入
import { getStudentStatsByMajor, getStudentStatsByGrade, getAllStudents } from '@/api/student'
import { getTeacherStatsByDepartment, getAllTeachers } from '@/api/teacher'
import { getCourseStatsByCredit, getAllCourses } from '@/api/course'
import { getAllClassrooms } from '@/api/classroom'
import { getMonthlyStats } from '@/api/statistics'

export default {
  name: 'Dashboard',
  components: {
    User,
    Avatar,
    Reading,
    School,
    TrendCharts,
    Management,
    House,
    Collection
  },
  setup() {
    const router = useRouter()
    
    // 图表实例
    const charts = reactive({
      studentMajor: null,
      studentGrade: null,
      teacherDepartment: null,
      courseCredit: null,
      trend: null
    })
    
    // 图表过滤器
    const chartFilters = reactive({
      studentMajorType: 'all',
      studentGradeView: 'bar',
      trendType: 'student'
    })
    
    // 统计数据
    const statistics = reactive({
      studentCount: 0,
      studentGrowth: 0,
      teacherCount: 0,
      teacherGrowth: 0,
      courseCount: 0,
      courseGrowth: 0,
      classroomCount: 0,
      classroomGrowth: 0
    })
    
    // 月度统计数据
    const monthlyStats = ref([])
    
    // 系统通知
    const activities = [
      {
        content: '系统更新：新增课程表导出功能',
        timestamp: '2023-06-25 10:30',
        type: 'primary',
        color: '#409EFF'
      },
      {
        content: '通知：2023学年第一学期课程安排已发布',
        timestamp: '2023-06-23 15:45',
        type: 'success',
        color: '#67C23A'
      },
      {
        content: '提醒：请教师尽快提交期末考试安排',
        timestamp: '2023-06-20 09:15',
        type: 'warning',
        color: '#E6A23C'
      },
      {
        content: '通知：系统将于本周凌晨进行维护',
        timestamp: '2023-06-18 11:00',
        type: 'info',
        color: '#909399'
      }
    ]
    
    // 快捷链接
    const quickLinks = [
      { title: '学生管理', icon: 'User', path: '/students' },
      { title: '教师管理', icon: 'Avatar', path: '/teachers' },
      { title: '课程管理', icon: 'Reading', path: '/courses' },
      { title: '教室管理', icon: 'School', path: '/classrooms' },
      { title: '宿舍管理', icon: 'House', path: '/dormitories' },
      { title: '图书管理', icon: 'Collection', path: '/books' }
    ]
    
    // 初始化学生专业分布图表
    const initStudentMajorChart = async () => {
      console.log('🔧 初始化学生专业分布图表')
      const chartDom = document.getElementById('studentMajorChart')
      console.log('📊 图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到学生专业分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 容器尺寸为0，延迟初始化')
        setTimeout(() => initStudentMajorChart(), 500)
        return
      }

      charts.studentMajor = echarts.init(chartDom)
      console.log('✅ ECharts实例创建成功:', charts.studentMajor)

      try {
        console.log('📡 开始获取学生专业分布数据')
        const res = await getStudentStatsByMajor()
        console.log('📊 API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 原始数据:', data)
        if (data) {
          // 将专业ID转换为专业名称
          const majorNames = {
            '1': '计算机科学与技术',
            '2': '软件工程',
            '3': '人工智能',
            '4': '网络工程',
            '5': '数学与应用数学',
            '6': '统计学',
            '7': '物理学',
            '8': '应用物理学',
            '9': '化学',
            '10': '生物科学'
          }

          // API返回的数据中key就是专业名称，直接使用
          console.log('🔄 转换后数据:', data)
          updateStudentMajorChart(data)
        }
      } catch (error) {
        console.error('获取学生专业分布数据失败:', error)

        // 显示错误信息，使用正确的图表配置
        safeSetChartOption('studentMajor', {
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              right: 10,
              top: 'center',
              data: []
            },
            series: [
              {
                name: '专业分布',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                data: [],
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                }
              }
            ]
          })
      }
    }
    
    // 更新学生专业分布图表
    const updateStudentMajorChart = (data) => {
      console.log('🎨 开始更新学生专业分布图表')
      console.log('📊 图表实例:', charts.studentMajor)
      console.log('📈 输入数据:', data)
      if (!charts.studentMajor) {
        console.error('❌ 图表实例不存在')
        return
      }

      let chartData = Object.entries(data).map(([name, value]) => ({ name, value }))
      console.log('🔄 处理后的图表数据:', chartData)
      
      // 根据过滤条件处理数据
      if (chartFilters.studentMajorType === 'top5') {
        chartData.sort((a, b) => b.value - a.value)
        chartData = chartData.slice(0, 5)
      }
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: chartData.map(item => item.name)
        },
        series: [
          {
            name: '专业分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
      
      console.log('⚙️ 图表配置:', option)
      safeSetChartOption('studentMajor', option)

      // 手动触发resize确保图表正确显示
      setTimeout(() => {
        try {
          const chart = safeAccessChart('studentMajor');
          
          if (!chart) {
            console.warn('⚠️ 专业图表不存在或无效，跳过resize');
            return;
          }
          
          // 检查图表是否已销毁
          if (typeof chart.isDisposed === 'function' && chart.isDisposed()) {
            console.warn('⚠️ 专业图表已销毁，跳过resize');
            return;
          }
          
          // 检查必要方法是否存在
          if (typeof chart.resize !== 'function' || typeof chart.getDom !== 'function') {
            console.warn('⚠️ 专业图表缺少必要方法，跳过resize');
            return;
          }
          
          // 检查DOM元素
          const dom = chart.getDom();
          if (!dom || dom.offsetWidth <= 0 || dom.offsetHeight <= 0) {
            console.warn('⚠️ 专业图表DOM无效或尺寸为0，跳过resize');
            return;
          }
          
          // 执行resize
          chart.resize();
          console.log('🔄 专业图表已resize');
        } catch (error) {
          console.error('❌ 专业图表resize失败:', error)
        }
      }, 100)
    }
    
    // 初始化学生年级分布图表
    let isStudentGradeChartInitializing = false
    const initStudentGradeChart = async () => {
      if (isStudentGradeChartInitializing) {
        console.warn('⚠️ 年级图表正在初始化，跳过本次调用')
        return
      }
      isStudentGradeChartInitializing = true
      try {
        console.log('🔧 初始化学生年级分布图表')
        const chartDom = document.getElementById('studentGradeChart')
        console.log('📊 年级图表容器:', chartDom)
        if (!chartDom) {
          console.error('❌ 找不到学生年级分布图表容器')
          isStudentGradeChartInitializing = false; // 解锁
          return
        }
    
        // 确保容器有明确的尺寸
        chartDom.style.width = '100%'
        chartDom.style.height = '300px'
        chartDom.style.minHeight = '300px'
        chartDom.style.display = 'block'
    
        // 等待容器完全渲染
        await nextTick()
    
        // 检查容器实际尺寸
        const rect = chartDom.getBoundingClientRect()
        console.log('📏 年级图表容器实际尺寸:', { width: rect.width, height: rect.height })
        if (rect.width === 0 || rect.height === 0) {
          console.error('❌ 年级图表容器尺寸为0，延迟初始化')
          isStudentGradeChartInitializing = false
          setTimeout(() => initStudentGradeChart(), 500)
          return
        }

        // 如果存在旧实例，先销毁
        if (charts.studentGrade && typeof charts.studentGrade.dispose === 'function' && !charts.studentGrade.isDisposed()) {
          console.log('♻️ 销毁旧的年级图表实例');
          charts.studentGrade.dispose();
        }

        charts.studentGrade = echarts.init(chartDom)
        console.log('✅ 年级图表ECharts实例创建成功:', charts.studentGrade)
        try {
          console.log('📡 开始获取学生年级分布数据')
          const res = await getStudentStatsByGrade()
          console.log('📊 年级API响应:', res)
          // 检查数据格式，可能直接是数据对象，也可能在res.data中
          const data = res.data || res
          console.log('📈 年级原始数据:', data)
          if (data) {
            // 将年份转换为年级名称
            const convertedData = {}
            Object.entries(data).forEach(([year, count]) => {
              convertedData[`${year}级`] = count
            })
            console.log('🔄 年级转换后数据:', convertedData)
            updateStudentGradeChart(convertedData)
          }
        } catch (error) {
          console.error('获取学生年级分布数据失败:', error)
                  safeSetChartOption('studentGrade', {
              title: {
                text: '数据加载失败',
                subtext: '请检查后端服务是否正常',
                left: 'center',
                top: 'middle',
                textStyle: {
                  color: '#999',
                  fontSize: 16
                }
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                }
              },
              xAxis: {
                type: 'category',
                data: [],
                axisLabel: {
                  interval: 0,
                  rotate: 30
                }
              },
              yAxis: {
                type: 'value'
              },
              series: [
                {
                  name: '学生人数',
                  type: 'bar',
                  data: [],
                  itemStyle: {
                    color: '#91cc75'
                  }
                }
              ]
            })
        }
      } finally {
        isStudentGradeChartInitializing = false
      }
    }
    
    // 更新学生年级分布图表
    const updateStudentGradeChart = (data) => {
      console.log('🎨 开始更新学生年级分布图表')
      console.log('📊 年级图表实例:', charts.studentGrade)
      console.log('📈 年级输入数据:', data)
      if (!charts.studentGrade) {
        console.error('❌ 年级图表实例不存在')
        return
      }
    
      // 数据有效性检查
      if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        console.warn('⚠️ 年级图表数据无效，使用默认配置')
        data = { '2020级': 0, '2021级': 0, '2022级': 0, '2023级': 0 }
      }
    
      const keys = Object.keys(data)
      const values = Object.values(data)
      console.log('🔄 年级处理后的数据:', { keys, values })
    
      // 先清空图表，避免配置冲突
      try {
        charts.studentGrade.clear()
        console.log('✅ 年级图表已清空')
      } catch (error) {
        console.warn('⚠️ 清空年级图表时出错:', error)
      }
    
      let option
      
      if (chartFilters.studentGradeView === 'bar') {
        option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: keys,
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '学生人数',
              type: 'bar',
              data: values,
              itemStyle: {
                color: '#91cc75'
              }
            }
          ]
        }
      } else {
        option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: keys
          },
          series: [
            {
              name: '年级分布',
              type: 'pie',
              radius: '70%',
              data: keys.map((key, index) => ({
                name: key,
                value: values[index]
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      }
    
      console.log('⚙️ 年级图表配置:', option)
      
      // 验证配置完整性，特别是series部分
      if (option.series && Array.isArray(option.series)) {
        option.series.forEach((serie, index) => {
          if (!serie.type) {
            console.warn(`⚠️ 年级图表第${index}个series缺少type属性，设置默认值`)
            serie.type = chartFilters.studentGradeView === 'bar' ? 'bar' : 'pie'
          }
          if (!serie.name) {
            serie.name = chartFilters.studentGradeView === 'bar' ? '学生人数' : '年级分布'
          }
          if (serie.type === 'bar' && !serie.data) {
            serie.data = []
          }
          if (serie.type === 'pie' && !serie.data) {
            serie.data = []
          }
        })
        console.log('✅ 年级图表series配置验证完成')
      } else {
        console.error('❌ 年级图表配置中缺少series数组')
        return
      }
      
      // 为了避免配置冲突，在设置配置前再次确保图表清空
      try {
        if (charts.studentGrade && typeof charts.studentGrade.clear === 'function') {
          charts.studentGrade.clear()
        }
      } catch (error) {
        console.warn('⚠️ 再次清空年级图表时出错:', error)
      }
      
      safeSetChartOption('studentGrade', option)
    
      // 强制resize
      setTimeout(() => {
        try {
          const chart = safeAccessChart('studentGrade');
          
          if (!chart) {
            console.warn('⚠️ 年级图表不存在或无效，跳过resize');
            return;
          }
          
          // 检查图表是否已销毁
          if (typeof chart.isDisposed === 'function' && chart.isDisposed()) {
            console.warn('⚠️ 年级图表已销毁，跳过resize');
            return;
          }
          
          // 检查必要方法是否存在
          if (typeof chart.resize !== 'function' || typeof chart.getDom !== 'function') {
            console.warn('⚠️ 年级图表缺少必要方法，跳过resize');
            return;
          }
          
          // 检查DOM元素
          const dom = chart.getDom();
          if (!dom || dom.offsetWidth <= 0 || dom.offsetHeight <= 0) {
            console.warn(`⚠️ 年级图表DOM无效或尺寸为0，跳过resize`);
            return;
          }
          
          // 执行resize
          chart.resize();
          console.log('✅ 年级图表resize成功');
        } catch (error) {
          console.error('❌ 年级图表resize过程中出错:', error);
        }
      }, 100)
    }
    
    // 初始化教师部门分布图表
    const initTeacherDepartmentChart = async () => {
      console.log('🔧 初始化教师部门分布图表')
      const chartDom = document.getElementById('teacherDepartmentChart')
      console.log('📊 部门图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到教师部门分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 部门图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 部门图表容器尺寸为0，延迟初始化')
        setTimeout(() => initTeacherDepartmentChart(), 500)
        return
      }

      charts.teacherDepartment = echarts.init(chartDom)
      console.log('✅ 部门图表ECharts实例创建成功:', charts.teacherDepartment)

      try {
        console.log('📡 开始获取教师部门分布数据')
        const res = await getTeacherStatsByDepartment()
        console.log('📊 部门API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 部门原始数据:', data)
        if (data) {
          // 将部门ID转换为部门名称
          const departmentNames = {
            '1': '计算机学院',
            '2': '数学学院',
            '3': '物理学院',
            '4': '化学学院',
            '5': '生物学院',
            '6': '外语学院',
            '7': '经济学院',
            '8': '管理学院',
            '9': '文学院',
            '10': '法学院'
          }

          const chartData = Object.entries(data).map(([id, value]) => ({
            name: departmentNames[id] || `部门${id}`,
            value
          }))
          console.log('🔄 部门转换后数据:', chartData)
          console.log('🎨 开始更新教师部门分布图表')
          console.log('📊 部门图表实例:', charts.teacherDepartment)

          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 20,
              bottom: 20,
              data: chartData.map(item => item.name)
            },
            series: [
              {
                name: '部门分布',
                type: 'pie',
                radius: '70%',
                center: ['40%', '50%'],
                data: chartData,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  formatter: '{b}: {c} ({d}%)'
                }
              }
            ]
          }

          console.log('⚙️ 部门图表配置:', option)
          safeSetChartOption('teacherDepartment', option)

          // 强制resize
          setTimeout(() => {
            try {
              const chart = safeAccessChart('teacherDepartment');
              
              if (!chart) {
                console.warn('⚠️ 部门图表不存在或无效，跳过resize');
                return;
              }
              
              // 检查图表是否已销毁
              if (typeof chart.isDisposed === 'function' && chart.isDisposed()) {
                console.warn('⚠️ 部门图表已销毁，跳过resize');
                return;
              }
              
              // 检查必要方法是否存在
              if (typeof chart.resize !== 'function' || typeof chart.getDom !== 'function') {
                console.warn('⚠️ 部门图表缺少必要方法，跳过resize');
                return;
              }
              
              // 检查DOM元素
              const dom = chart.getDom();
              if (!dom || dom.offsetWidth <= 0 || dom.offsetHeight <= 0) {
                console.warn('⚠️ 部门图表DOM无效或尺寸为0，跳过resize');
                return;
              }
              
              // 执行resize
              chart.resize();
              console.log('🔄 部门图表已resize');
            } catch (error) {
              console.error('❌ 部门图表resize失败:', error)
            }
          }, 100)
        }
      } catch (error) {
        console.error('获取教师部门分布数据失败:', error)

        // 显示错误信息，使用正确的图表配置
        safeSetChartOption('teacherDepartment', {
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 20,
              bottom: 20,
              data: []
            },
            series: [
              {
                name: '部门分布',
                type: 'pie',
                radius: '60%',
                center: ['40%', '50%'],
                data: [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  formatter: '{b}: {c} ({d}%)'
                }
              }
            ]
          })
      }
    }
    
    // 标志，防止重复初始化
    let isCourseCreditChartInitializing = false;

    // 初始化课程学分分布图表
    const initCourseCreditChart = async () => {
      if (isCourseCreditChartInitializing) {
        console.warn('⚠️ 课程学分图表正在初始化中，跳过本次请求');
        return;
      }
      isCourseCreditChartInitializing = true;

      console.log('🔧 初始化课程学分分布图表')
      const chartDom = document.getElementById('courseCreditChart')
      console.log('📊 学分图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到课程学分分布图表容器')
        isCourseCreditChartInitializing = false; // 解锁
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 学分图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 学分图表容器尺寸为0，延迟初始化')
        isCourseCreditChartInitializing = false; // 解锁
        setTimeout(() => initCourseCreditChart(), 500) // 延迟后重试
        return
      }

      // 如果存在旧实例，先销毁
      if (charts.courseCredit && typeof charts.courseCredit.dispose === 'function' && !charts.courseCredit.isDisposed()) {
        console.log('♻️ 销毁旧的课程学分图表实例');
        charts.courseCredit.dispose();
      }

      charts.courseCredit = echarts.init(chartDom)
      console.log('✅ 学分图表ECharts实例创建成功:', charts.courseCredit)
      isCourseCreditChartInitializing = false; // 初始化完成，解锁

      try {
        console.log('📡 开始获取课程学分分布数据')
        const res = await getCourseStatsByCredit()
        console.log('📊 学分API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 学分原始数据:', data)
        if (data) {
          const keys = Object.keys(data)
          const values = Object.values(data)
          console.log('🔄 学分数据处理:', { keys, values })
          console.log('🎨 开始更新课程学分分布图表')
          console.log('📊 学分图表实例:', charts.courseCredit)

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            xAxis: {
              type: 'category',
              data: keys,
              axisLabel: {
                interval: 0
              }
            },
            yAxis: {
              type: 'value',
              name: '课程数量'
            },
            series: [
              {
                name: '课程数量',
                type: 'bar',
                data: values,
                itemStyle: {
                  color: '#5470c6'
                }
              }
            ]
          }

          console.log('⚙️ 学分图表配置:', option)
          safeSetChartOption('courseCredit', option)

          // 强制resize
          setTimeout(() => {
            try {
              // 在每次访问前重新获取chart引用，避免竞态条件
              let chart = charts.courseCredit;
              
              // 更全面的图表有效性检查
              if (!chart || typeof chart !== 'object') {
                console.warn('⚠️ 学分图表不存在或无效，跳过resize');
                return;
              }
              
              // 重新检查chart引用，防止在检查过程中被修改
              chart = charts.courseCredit;
              if (!chart || typeof chart !== 'object') {
                console.warn('⚠️ 学分图表在检查过程中变为无效，跳过resize');
                return;
              }
              
              // 检查图表是否已销毁
              if (chart && typeof chart.isDisposed === 'function' && chart.isDisposed()) {
                console.warn('⚠️ 学分图表已销毁，跳过resize');
                return;
              }
              
              // 再次验证chart引用
              chart = charts.courseCredit;
              if (!chart || typeof chart !== 'object') {
                console.warn('⚠️ 学分图表在isDisposed检查后变为无效，跳过resize');
                return;
              }
              
              // 检查resize方法是否存在
              if (!chart || typeof chart.resize !== 'function') {
                console.warn('⚠️ 学分图表没有resize方法，跳过resize');
                return;
              }
              
              // 再次验证chart引用
              chart = charts.courseCredit;
              if (!chart || typeof chart !== 'object') {
                console.warn('⚠️ 学分图表在resize方法检查后变为无效，跳过resize');
                return;
              }
              
              // 检查getDom方法是否存在
              if (!chart || typeof chart.getDom !== 'function') {
                console.warn('⚠️ 学分图表没有getDom方法，跳过resize');
                return;
              }
              
              // 再次验证chart引用并检查DOM元素
              chart = charts.courseCredit;
              if (!chart || typeof chart !== 'object') {
                console.warn('⚠️ 学分图表在getDom检查后变为无效，跳过resize');
                return;
              }
              
              const dom = chart.getDom();
              if (!dom) {
                console.warn('⚠️ 学分图表DOM元素不存在，跳过resize');
                return;
              }
              
              // 检查DOM元素尺寸是否有效
              if (dom.offsetWidth <= 0 || dom.offsetHeight <= 0) {
                console.warn(`⚠️ 学分图表容器尺寸无效: ${dom.offsetWidth}x${dom.offsetHeight}，跳过resize`);
                return;
              }
              
              // 最终验证chart引用并执行resize
              chart = charts.courseCredit;
              if (chart && typeof chart === 'object' && typeof chart.resize === 'function') {
                chart.resize();
                console.log('✅ 学分图表resize成功');
              } else {
                console.warn('⚠️ 学分图表在执行resize前变为无效，跳过resize');
              }
            } catch (error) {
              console.error('❌ 学分图表resize过程中出错:', error);
            }
          }, 100)
        }
      } catch (error) {
        console.error('获取课程学分分布数据失败:', error)

        // 显示错误信息，使用正确的图表配置
        safeSetChartOption('courseCredit', {
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            tooltip: {},
            xAxis: {
              type: 'category',
              data: []
            },
            yAxis: {
              type: 'value',
              name: '课程数量'
            },
            series: [
              {
                name: '课程数量',
                type: 'bar',
                data: [],
                itemStyle: {
                  color: '#5470c6'
                }
              }
            ]
          })
      }
    }
    
    // 加载真实统计数据
    const loadRealStatistics = async () => {
      console.log('🔧 开始加载真实统计数据')
      try {
        // 获取学生总数
        const studentsRes = await getAllStudents()
        const students = studentsRes.data || studentsRes || []
        statistics.studentCount = students.length
        console.log('📊 学生总数:', statistics.studentCount)

        // 获取教师总数
        const teachersRes = await getAllTeachers()
        const teachers = teachersRes.data || teachersRes || []
        statistics.teacherCount = teachers.length
        console.log('📊 教师总数:', statistics.teacherCount)

        // 获取课程总数
        const coursesRes = await getAllCourses()
        const courses = coursesRes.data || coursesRes || []
        statistics.courseCount = courses.length
        console.log('📊 课程总数:', statistics.courseCount)

        // 获取教室总数
        const classroomsRes = await getAllClassrooms()
        const classrooms = classroomsRes.data || classroomsRes || []
        statistics.classroomCount = classrooms.length
        console.log('📊 教室总数:', statistics.classroomCount)

        // 获取月度统计数据
        const monthlyRes = await getMonthlyStats()
        monthlyStats.value = monthlyRes.data || monthlyRes || []
        console.log('📊 月度统计数据:', monthlyStats.value)

        console.log('✅ 统计数据加载完成')
      } catch (error) {
        console.error('❌ 加载统计数据失败:', error)
        // 显示错误状态，不使用模拟数据
        statistics.studentCount = 0
        statistics.teacherCount = 0
        statistics.courseCount = 0
        statistics.classroomCount = 0
        monthlyStats.value = []
      }
    }

    // 初始化趋势图表
    const initTrendChart = () => {
      const chartDom = document.getElementById('trendChart')
      if (!chartDom) {
        console.error('❌ 找不到趋势图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 趋势图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 趋势图表容器尺寸为0，延迟初始化')
        setTimeout(() => initTrendChart(), 500)
        return
      }

      charts.trend = echarts.init(chartDom)
      console.log('✅ 趋势图表ECharts实例创建成功:', charts.trend)
      updateTrendChart()
    }
    
    // 更新趋势图表数据
    const updateTrendChart = () => {
      // 修复：正确访问趋势图表实例
      const chart = charts.trend;
      if (!chart) return;

      let seriesData = [];
      let title = '';

      switch (chartFilters.trendType) {
        case 'student':
          title = '学生数量月度趋势';
          seriesData = monthlyStats.map(item => item.studentCount);
          break;
        case 'teacher':
          title = '教师数量月度趋势';
          seriesData = monthlyStats.map(item => item.teacherCount);
          break;
        case 'course':
          title = '课程数量月度趋势';
          seriesData = monthlyStats.map(item => item.courseCount);
          break;
      }

      const months = monthlyStats.map(item => item.month);

      const option = {
        title: { text: title },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: months
        },
        yAxis: { type: 'value' },
        series: [{
          data: seriesData,
          type: 'line',
          smooth: true
        }]
      };

      chart.setOption(option);
    };
    
    // 导航到指定路径
    const navigateTo = (path) => {
      router.push(path)
    }
    
    // 安全访问图表的工具函数
    const safeAccessChart = (chartKey) => {
      try {
        // 先检查charts对象中是否存在该键
        if (!(chartKey in charts)) {
          console.warn(`⚠️ ${chartKey}在charts对象中不存在`);
          return null;
        }
        
        const chart = charts[chartKey];
        
        // 检查chart是否为有效对象
        if (!chart || typeof chart !== 'object') {
          console.warn(`⚠️ ${chartKey}图表不存在或无效`);
          return null;
        }
        
        return chart;
      } catch (error) {
        console.warn(`⚠️ 访问${chartKey}图表时出错:`, error);
        return null;
      }
    };

    // 安全设置图表配置的工具函数
    const safeSetChartOption = (chartKey, option) => {
      try {
        // 先检查charts对象中是否存在该键
        if (!(chartKey in charts)) {
          console.warn(`⚠️ ${chartKey}在charts对象中不存在，无法设置配置`);
          return false;
        }
        
        const chart = charts[chartKey];
        
        // 检查chart是否为有效对象
        if (!chart || typeof chart !== 'object') {
          console.warn(`⚠️ ${chartKey}图表不存在或无效，无法设置配置`);
          return false;
        }
        
        // 检查setOption方法是否存在
        if (typeof chart.setOption !== 'function') {
          console.warn(`⚠️ ${chartKey}图表缺少setOption方法，无法设置配置`);
          return false;
        }

        // 验证配置对象
        if (!option || typeof option !== 'object') {
          console.warn(`⚠️ ${chartKey}图表配置无效:`, option);
          return false;
        }

        // 深度克隆配置以防止引用问题
        const safeOption = JSON.parse(JSON.stringify(option));
        
        // 验证配置中的series数组
        if (safeOption.series && Array.isArray(safeOption.series)) {
          safeOption.series.forEach((serie, index) => {
            if (!serie.type) {
              console.warn(`⚠️ ${chartKey}图表第${index}个series缺少type属性，设置为默认值'line'`);
              serie.type = 'line';
            }
          });
        }
        
        chart.setOption(safeOption, true); // 第二个参数为true表示不合并配置
        console.log(`✅ ${chartKey}图表配置设置成功`);
        return true;
      } catch (error) {
        console.error(`❌ 设置${chartKey}图表配置时出错:`, error);
        return false;
      }
    };

    // 防抖处理resize事件
    let resizeTimer = null;
    const debouncedHandleResize = () => {
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
      resizeTimer = setTimeout(() => {
        handleResize();
      }, 150); // 150ms防抖延迟
    };

    // 监听窗口大小变化，重新绘制图表
    const handleResize = () => {
      // 逐个检查并resize每个图表，避免一个图表的错误影响其他图表
      for (const chartKey in charts) {
        try {
          // 先检查charts对象中是否存在该键
          if (!(chartKey in charts)) {
            console.warn(`⚠️ ${chartKey}在charts对象中不存在，跳过resize`);
            continue;
          }
          
          const chart = charts[chartKey];
          
          // 检查chart是否为有效对象
          if (!chart || typeof chart !== 'object') {
            console.warn(`⚠️ ${chartKey}图表不存在或无效，跳过resize`);
            continue;
          }
          
          // 检查图表是否已销毁
          if (typeof chart.isDisposed === 'function' && chart.isDisposed()) {
            console.warn(`⚠️ ${chartKey}图表已销毁，跳过resize`);
            continue;
          }
          
          // 检查必要方法是否存在
          if (typeof chart.resize !== 'function') {
            console.warn(`⚠️ ${chartKey}图表缺少resize方法，跳过resize`);
            continue;
          }
          
          if (typeof chart.getDom !== 'function') {
            console.warn(`⚠️ ${chartKey}图表缺少getDom方法，跳过resize`);
            continue;
          }
          
          // 检查DOM元素
          const dom = chart.getDom();
          if (!dom) {
            console.warn(`⚠️ ${chartKey}图表DOM元素不存在，跳过resize`);
            continue;
          }

          if (dom.offsetWidth <= 0 || dom.offsetHeight <= 0) {
            console.warn(`⚠️ ${chartKey}图表DOM尺寸无效: ${dom.offsetWidth}x${dom.offsetHeight}，跳过resize`);
            continue;
          }
          
          // 执行resize
          chart.resize();
          console.log(`✅ ${chartKey}图表resize成功`);
        } catch (error) {
          console.error(`❌ ${chartKey}图表resize过程中出错:`, error);
          // 记录错误但不立即尝试恢复，避免无限循环
        }
      }
    }

    // 图表错误恢复机制
    const recoverCharts = () => {
      console.log('🔄 尝试恢复图表')
      const chartMap = {
        'studentMajorChart': 'studentMajor',
        'studentGradeChart': 'studentGrade',
        'teacherDepartmentChart': 'teacherDepartment',
        'courseCreditChart': 'courseCredit',
        'trendChart': 'trend'
      }

      // 检查每个图表容器和实例
      Object.entries(chartMap).forEach(([domId, chartKey]) => {
        try {
          // 首先检查DOM元素是否存在
          const element = document.getElementById(domId)
          if (!element) {
            console.warn(`⚠️ 图表容器不存在: ${domId}`)
            return
          }
          
          // 检查图表实例状态
          const chart = charts[chartKey]
          
          // 更精确地判断图表是否需要恢复
          const needsRecovery = !chart || 
                              (typeof chart !== 'object') || 
                              (typeof chart.isDisposed === 'function' && chart.isDisposed()) ||
                              (typeof chart.resize !== 'function') ||
                              (typeof chart.getDom !== 'function')
          
          // 只有当DOM元素存在且图表实例需要恢复时才重新初始化
          if (needsRecovery) {
            console.log(`🔧 开始恢复图表: ${domId} -> ${chartKey}`)
            
            // 确保容器有明确的尺寸和可见性
            element.style.width = '100%'
            element.style.height = '300px'
            element.style.minHeight = '300px'
            element.style.display = 'block'
            
            // 延迟初始化以确保DOM已更新
            setTimeout(() => {
              try {
                // 先检查DOM元素是否仍然存在
                const currentElement = document.getElementById(domId)
                if (!currentElement) {
                  console.warn(`⚠️ 恢复前图表容器已不存在: ${domId}`)
                  return
                }
                
                // 检查容器尺寸
                const rect = currentElement.getBoundingClientRect()
                if (rect.width === 0 || rect.height === 0) {
                  console.warn(`⚠️ 图表容器尺寸无效: ${domId}, 宽度=${rect.width}, 高度=${rect.height}`)
                  // 再次尝试设置尺寸
                  currentElement.style.width = '100%'
                  currentElement.style.height = '300px'
                  currentElement.style.minHeight = '300px'
                  currentElement.style.display = 'block'
                }
                
                // 根据图表类型调用相应的初始化函数
                if (chartKey === 'studentMajor') {
                  initStudentMajorChart()
                  console.log(`✅ 成功恢复学生专业图表`)
                }
                else if (chartKey === 'studentGrade') {
                  initStudentGradeChart()
                  console.log(`✅ 成功恢复学生年级图表`)
                }
                else if (chartKey === 'teacherDepartment') {
                  initTeacherDepartmentChart()
                  console.log(`✅ 成功恢复教师部门图表`)
                }
                else if (chartKey === 'courseCredit') {
                  initCourseCreditChart()
                  console.log(`✅ 成功恢复课程学分图表`)
                }
                else if (chartKey === 'trend') {
                  initTrendChart()
                  console.log(`✅ 成功恢复趋势图表`)
                }
              } catch (error) {
                console.error(`❌ 恢复${chartKey}图表失败:`, error)
              }
            }, 300) // 增加延迟时间，确保DOM完全准备好
          } else {
            console.log(`✓ ${chartKey}图表状态正常，无需恢复`)
          }
        } catch (error) {
          console.error(`❌ 处理${chartKey}图表恢复过程中出错:`, error)
        }
      })
    }
    
    // 监听图表过滤器变化
    watch(() => chartFilters.studentMajorType, async () => {
      try {
        const res = await getStudentStatsByMajor()
        if (res.data) {
          updateStudentMajorChart(res.data)
        }
      } catch (error) {
        console.error('获取学生专业分布数据失败:', error)
        // 不使用模拟数据，让图表显示错误状态
      }
    })
    
    watch(() => chartFilters.studentGradeView, async (newVal, oldVal) => {
      console.log('🔄 学生年级图表类型切换:', oldVal, '->', newVal)
      
      if (charts.studentGrade) {
        // 先销毁当前图表实例
        try {
          charts.studentGrade.dispose()
          console.log('♻️ 年级图表实例已销毁，准备重新创建')
        } catch (error) {
          console.warn('⚠️ 销毁年级图表实例时出错:', error)
        }
        
        // 清空图表引用
        charts.studentGrade = null
      }
      
      // 延迟重新初始化，避免DOM操作冲突
      setTimeout(async () => {
        try {
          await initStudentGradeChart()
          console.log('✅ 年级图表重新初始化完成')
        } catch (error) {
          console.error('❌ 重新初始化年级图表失败:', error)
        }
      }, 150) // 稍微增加延迟时间
    })
    
    watch(() => chartFilters.trendType, () => {
      updateTrendChart()
    })
    
    // 初始化所有图表的函数
    const initAllCharts = async () => {
      console.log('🚀 开始初始化所有图表')

      // 检查所有图表容器是否存在
      const containers = [
        'studentMajorChart',
        'studentGradeChart',
        'teacherDepartmentChart',
        'courseCreditChart',
        'trendChart'
      ]

      let allContainersReady = true
      containers.forEach(id => {
        const element = document.getElementById(id)
        console.log(`📦 容器 ${id}:`, element)
        if (element) {
          const rect = element.getBoundingClientRect()
          console.log(`📏 容器 ${id} 尺寸:`, {
            width: rect.width,
            height: rect.height,
            display: getComputedStyle(element).display,
            visibility: getComputedStyle(element).visibility
          })
          if (rect.width === 0 || rect.height === 0) {
            allContainersReady = false
          }
        } else {
          allContainersReady = false
        }
      })

      if (!allContainersReady) {
        console.log('⏳ 容器未完全准备好，延迟初始化')
        setTimeout(initAllCharts, 200)
        return
      }

      // 依次初始化图表
      try {
        await initStudentMajorChart()
        await initStudentGradeChart()
        await initTeacherDepartmentChart()
        await initCourseCreditChart()
        initTrendChart()
        console.log('✅ 所有图表初始化完成')
      } catch (error) {
        console.error('❌ 图表初始化失败:', error)
      }
    }

    // 组件挂载后初始化图表
    onMounted(async () => {
      await nextTick()

      // 先加载真实统计数据
      await loadRealStatistics()

      // 使用requestAnimationFrame确保DOM完全渲染
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          initAllCharts()
        })
      })

      window.addEventListener('resize', debouncedHandleResize)
    })
    
    // 组件卸载前清理图表实例和事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', debouncedHandleResize)
      
      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer);
        resizeTimer = null;
      }
      
      Object.values(charts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
    })
    
    return {
      statistics,
      activities,
      quickLinks,
      chartFilters,
      navigateTo,
      monthlyStats
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.card-row {
  margin-bottom: 20px;
}

.data-card {
  height: 120px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-content {
  height: 85px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.bg-primary {
  background-color: #409EFF;
  color: white;
}

.bg-success {
  background-color: #67C23A;
  color: white;
}

.bg-warning {
  background-color: #E6A23C;
  color: white;
}

.bg-danger {
  background-color: #F56C6C;
  color: white;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 16px;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
}

.card-footer {
  background-color: #f5f7fa;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #606266;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 10px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  min-height: 300px;
  width: 100%;
  position: relative;
  display: block;
}

/* 确保所有图表容器都有正确的尺寸 */
#studentMajorChart,
#studentGradeChart,
#teacherDepartmentChart,
#courseCreditChart,
#trendChart {
  width: 100% !important;
  height: 300px !important;
  min-height: 300px !important;
  display: block !important;
}

.quick-links {
  padding: 10px 0;
}

.quick-link-item {
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.quick-link-item:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.quick-link-item .el-icon {
  color: #409EFF;
  margin-bottom: 10px;
}

.quick-link-item span {
  font-size: 14px;
}

@media (max-width: 768px) {
  .data-card {
    margin-bottom: 20px;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style>