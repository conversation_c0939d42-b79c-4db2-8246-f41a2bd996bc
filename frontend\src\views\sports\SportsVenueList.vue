<template>
  <div class="sports-venue-list-container">
    <div class="page-header">
      <h2>体育场馆管理</h2>
      <el-button type="primary" @click="handleAddVenue">新增场馆</el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="场馆名称">
          <el-input v-model="queryParams.name" placeholder="请输入场馆名称" clearable />
        </el-form-item>
        <el-form-item label="场馆类型">
          <el-select v-model="queryParams.type" placeholder="请选择场馆类型" clearable>
            <el-option label="篮球场" value="篮球场" />
            <el-option label="足球场" value="足球场" />
            <el-option label="网球场" value="网球场" />
            <el-option label="羽毛球馆" value="羽毛球馆" />
            <el-option label="乒乓球室" value="乒乓球室" />
            <el-option label="游泳馆" value="游泳馆" />
            <el-option label="健身房" value="健身房" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="开放" :value="0" />
            <el-option label="维修中" :value="1" />
            <el-option label="已预约" :value="2" />
            <el-option label="关闭" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-container">
      <el-table
        v-loading="loading"
        :data="venueList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="name" label="场馆名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="type" label="场馆类型" width="120" />
        <el-table-column prop="location" label="位置" min-width="150" show-overflow-tooltip />
        <el-table-column prop="capacity" label="容量" width="80" />
        <el-table-column prop="openTime" label="开放时间" width="120" />
        <el-table-column prop="closeTime" label="关闭时间" width="120" />
        <el-table-column prop="fee" label="费用" width="80">
          <template #default="scope">
            {{ scope.row.fee > 0 ? `${scope.row.fee}元/小时` : '免费' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:page-size="queryParams.pageSize"
          v-model:current-page="queryParams.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入API
import { getSportsVenuesByPage, deleteSportsVenue } from '@/api/sportsVenue'

export default {
  name: 'SportsVenueList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const total = ref(0)
    const venueList = ref([])
    
    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      name: '',
      type: '',
      status: undefined
    })
    
    // 获取场馆列表
    const getList = async () => {
      loading.value = true
      try {
        // 调用API获取场馆列表
        const response = await getSportsVenuesByPage(queryParams)
        venueList.value = response.data.list
        total.value = response.data.total
        loading.value = false
      } catch (error) {
        loading.value = false
        ElMessage.error('获取场馆列表失败')
        console.error(error)
      }
    }
    
    // 获取状态标签类型
    const getStatusType = (status) => {
      switch (status) {
        case 0: return 'success' // 开放
        case 1: return 'warning' // 维修中
        case 2: return 'info'    // 已预约
        case 3: return 'danger'  // 关闭
        default: return ''
      }
    }
    
    // 获取状态标签文本
    const getStatusLabel = (status) => {
      switch (status) {
        case 0: return '开放'
        case 1: return '维修中'
        case 2: return '已预约'
        case 3: return '关闭'
        default: return '未知'
      }
    }
    
    // 处理查询按钮点击
    const handleQuery = () => {
      queryParams.pageNum = 1
      getList()
    }
    
    // 重置查询条件
    const resetQuery = () => {
      queryParams.name = ''
      queryParams.type = ''
      queryParams.status = undefined
      queryParams.pageNum = 1
      getList()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      queryParams.pageNum = val
      getList()
    }
    
    // 处理每页数量变化
    const handleSizeChange = (val) => {
      queryParams.pageSize = val
      queryParams.pageNum = 1
      getList()
    }
    
    // 查看场馆详情
    const handleView = (row) => {
      ElMessage.info(`查看场馆：${row.name}`)
    }
    
    // 编辑场馆
    const handleEdit = (row) => {
      router.push({ name: 'SportsVenueEdit', params: { id: row.id } })
    }
    
    // 添加场馆
    const handleAddVenue = () => {
      router.push({ name: 'SportsVenueAdd' })
    }
    
    // 删除场馆
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除场馆${row.name}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用API删除场馆
          await deleteSportsVenue(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      loading,
      venueList,
      queryParams,
      total,
      handleQuery,
      resetQuery,
      handleCurrentChange,
      handleSizeChange,
      handleView,
      handleEdit,
      handleAddVenue,
      handleDelete,
      getStatusType,
      getStatusLabel
    }
  }
}
</script>

<style scoped>
.sports-venue-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}
</style>