
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.10)

2025-07-27 09:38:14 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication v1.0.0 using Java 24.0.1 on BF-202504030152 with PID 34564 (D:\1项目解析\大学学生管理系统\backend\target\university-management-1.0.0.jar started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 09:38:14 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 09:38:14 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 09:38:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 496 ms. Found 11 JPA repository interfaces.
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 09:38:19 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 09:38:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 09:38:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 09:38:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 09:38:21 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 09:38:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6603 ms
2025-07-27 09:38:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 09:38:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
Hibernate: 
    
    alter table sys_role_menu 
       add constraint FKkeitxsgxwayackgqllio4ohn5 
       foreign key (role_id) 
       references sys_role (id)
2025-07-27 09:38:26 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:38:27 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 09:38:30 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 994d6a5a-4c19-48c0-856e-f2ae5880a661

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 09:38:30 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2bba35ef, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@26c7b1c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@77c66a4f, org.springframework.security.web.header.HeaderWriterFilter@3cc9632d, org.springframework.web.filter.CorsFilter@14f5da2c, org.springframework.security.web.authentication.logout.LogoutFilter@7cf8f45a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7a3f08b6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@615439f7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19526f1d, org.springframework.security.web.session.SessionManagementFilter@55d8c2c4, org.springframework.security.web.access.ExceptionTranslationFilter@45bbc52f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@65bd19bf]
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Property 'mapperLocations' was not specified.
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.3.1 
2025-07-27 09:38:32 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-07-27 09:38:32 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 09:38:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-27 09:38:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-27 09:38:33 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-27 09:38:33 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-27 09:38:33 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

