# 应用名称
spring.application.name=university-management-system

# 服务器配置
server.port=8083
server.servlet.context-path=/

# 数据库配置
spring.datasource.url=**********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=7121020qing
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 数据库初始化
spring.sql.init.mode=always
spring.sql.init.continue-on-error=true
spring.sql.init.schema-locations=classpath:db/init-schema.sql
spring.sql.init.data-locations=classpath:db/init-data.sql
spring.jpa.defer-datasource-initialization=true

# JPA配置
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=false

# Jackson配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.deserialization.FAIL_ON_UNKNOWN_PROPERTIES=false
spring.jackson.default-property-inclusion=non_null

# 添加字符编码设置
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# 添加字符编码设置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true


# 日志配置
logging.level.root=info
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.com.university.management=debug
logging.file.name=logs/university-management.log
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Swagger配置
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
springfox.documentation.swagger-ui.enabled=true
springfox.documentation.swagger.v2.enabled=true

# Redis配置 - 已禁用
# spring.redis.host=localhost
# spring.redis.port=6379
# spring.redis.password=
# spring.redis.database=0
# spring.redis.connect-timeout=5000
# spring.redis.client-type=jedis
# 禁用Redis
spring.cache.type=simple
spring.data.redis.repositories.enabled=false

# JWT配置
jwt.secret=${JWT_SECRET:universityManagementSecretKey}
jwt.expiration=${JWT_EXPIRATION:86400000}

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB 

# Knife4j配置
knife4j.enable=true
knife4j.setting.language=zh-CN
knife4j.setting.swagger-model-name=实体类列表